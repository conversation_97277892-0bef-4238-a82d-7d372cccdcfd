import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase';

/**
 * GET /api/dependencias
 * Obtiene lista de dependencias con métricas de contenido
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search');
    const hasContent = searchParams.get('hasContent'); // 'tramites', 'opas', 'faqs', 'any'
    const sortBy = searchParams.get('sortBy') || 'nombre'; // 'nombre', 'total_contenido'
    const order = searchParams.get('order') || 'asc'; // 'asc', 'desc'
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    const supabase = await createServerSupabase();

    // Consultar solo dependencias principales con métricas calculadas
    let query = supabase
      .schema('ingestion')
      .from('dependencias')
      .select(`
        id,
        codigo,
        nombre,
        descripcion,
        sigla,
        activo,
        created_at,
        updated_at
      `)
      .eq('activo', true);

    // Aplicar filtro de búsqueda
    if (search) {
      query = query.ilike('nombre', `%${search}%`);
    }

    // Aplicar filtro por tipo de contenido
    if (hasContent) {
      switch (hasContent) {
        case 'tramites':
          query = query.gt('total_tramites', 0);
          break;
        case 'opas':
          query = query.gt('total_opas', 0);
          break;
        case 'faqs':
          query = query.gt('total_faqs', 0);
          break;
        case 'any':
          query = query.gt('total_contenido', 0);
          break;
      }
    }

    // Aplicar ordenamiento
    const ascending = order === 'asc';
    query = query.order(sortBy, { ascending });

    // Aplicar paginación
    query = query.range(offset, offset + limit - 1);

    const { data: dependenciasRaw, error, count } = await query;

    if (error) {
      console.error('Error fetching dependencias:', error);
      return NextResponse.json(
        { error: 'Error al obtener dependencias' },
        { status: 500 }
      );
    }

    // Calcular métricas para cada dependencia
    const dependencias = await Promise.all(
      (dependenciasRaw || []).map(async (dep) => {
        // Contar subdependencias
        const { count: subdependenciasCount } = await supabase
          .schema('ingestion')
          .from('subdependencias')
          .select('*', { count: 'exact', head: true })
          .eq('dependencia_id', dep.id)
          .eq('activo', true);

        // Contar trámites
        const { count: tramitesCount } = await supabase
          .schema('ingestion')
          .from('tramites')
          .select('*', { count: 'exact', head: true })
          .eq('dependencia_id', dep.id)
          .eq('activo', true);

        // Contar OPAs
        const { count: opasCount } = await supabase
          .schema('ingestion')
          .from('opas')
          .select('*', { count: 'exact', head: true })
          .eq('dependencia_id', dep.id)
          .eq('activo', true);

        // Contar FAQs
        const { count: faqsCount } = await supabase
          .schema('ingestion')
          .from('faqs')
          .select('*', { count: 'exact', head: true })
          .eq('dependencia_id', dep.id)
          .eq('activo', true);

        return {
          ...dep,
          total_subdependencias: subdependenciasCount || 0,
          total_tramites: tramitesCount || 0,
          total_opas: opasCount || 0,
          total_faqs: faqsCount || 0,
          total_contenido: (tramitesCount || 0) + (opasCount || 0) + (faqsCount || 0)
        };
      })
    );

    // Calcular estadísticas generales a partir de las dependencias procesadas

    type StatsAccumulator = {
      total_dependencias: number;
      total_subdependencias: number;
      total_tramites: number;
      total_opas: number;
      total_faqs: number;
      total_contenido: number;
    };

    const totalStats = dependencias.reduce((acc: StatsAccumulator, dep) => ({
      total_dependencias: acc.total_dependencias + 1,
      total_subdependencias: acc.total_subdependencias + dep.total_subdependencias,
      total_tramites: acc.total_tramites + dep.total_tramites,
      total_opas: acc.total_opas + dep.total_opas,
      total_faqs: acc.total_faqs + dep.total_faqs,
      total_contenido: acc.total_contenido + dep.total_contenido
    }), {
      total_dependencias: 0,
      total_subdependencias: 0,
      total_tramites: 0,
      total_opas: 0,
      total_faqs: 0,
      total_contenido: 0
    } as StatsAccumulator);

    return NextResponse.json({
      success: true,
      data: dependencias,
      pagination: {
        limit,
        offset,
        total: count || 0
      },
      stats: totalStats
    });

  } catch (error) {
    console.error('Unexpected error in dependencias API:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/dependencias
 * Acciones adicionales para dependencias
 */
export async function POST(request: NextRequest) {
  try {
    const { action } = await request.json();
    
    if (action === 'get_stats') {
      const supabase = await createServerSupabase();

      // Obtener estadísticas detalladas - reutilizar la lógica del GET
      const response = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/dependencias?limit=100`);
      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error);
      }

      const dependencias = data.data;

      if (error) {
        throw error;
      }

      const stats = {
        total_dependencias: dependencias?.length || 0,
        total_subdependencias: dependencias?.reduce((sum, d) => sum + d.total_subdependencias, 0) || 0,
        total_tramites: dependencias?.reduce((sum, d) => sum + d.total_tramites, 0) || 0,
        total_opas: dependencias?.reduce((sum, d) => sum + d.total_opas, 0) || 0,
        total_faqs: dependencias?.reduce((sum, d) => sum + d.total_faqs, 0) || 0,
        total_contenido: dependencias?.reduce((sum, d) => sum + d.total_contenido, 0) || 0,
        top_dependencias: dependencias?.slice(0, 5) || [],
        dependencias_sin_contenido: dependencias?.filter(d => d.total_contenido === 0).length || 0
      };

      return NextResponse.json({
        success: true,
        data: stats
      });
    }

    return NextResponse.json(
      { error: 'Acción no válida' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Error in dependencias POST:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
