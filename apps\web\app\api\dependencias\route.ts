import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase';

/**
 * GET /api/dependencias
 * Obtiene lista de dependencias con métricas de contenido
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search');
    const hasContent = searchParams.get('hasContent'); // 'tramites', 'opas', 'faqs', 'any'
    const sortBy = searchParams.get('sortBy') || 'nombre'; // 'nombre', 'total_contenido'
    const order = searchParams.get('order') || 'asc'; // 'asc', 'desc'
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    const supabase = await createServerSupabase();

    let query = supabase
      .from('dependencias_view')
      .select('*');

    // Aplicar filtro de búsqueda
    if (search) {
      query = query.ilike('nombre', `%${search}%`);
    }

    // Aplicar filtro por tipo de contenido
    if (hasContent) {
      switch (hasContent) {
        case 'tramites':
          query = query.gt('total_tramites', 0);
          break;
        case 'opas':
          query = query.gt('total_opas', 0);
          break;
        case 'faqs':
          query = query.gt('total_faqs', 0);
          break;
        case 'any':
          query = query.gt('total_contenido', 0);
          break;
      }
    }

    // Aplicar ordenamiento
    const ascending = order === 'asc';
    query = query.order(sortBy, { ascending });

    // Aplicar paginación
    query = query.range(offset, offset + limit - 1);

    const { data: dependencias, error, count } = await query;

    if (error) {
      console.error('Error fetching dependencias:', error);
      return NextResponse.json(
        { error: 'Error al obtener dependencias' },
        { status: 500 }
      );
    }

    // Obtener estadísticas generales
    const { data: stats } = await supabase
      .from('dependencias_view')
      .select('total_subdependencias, total_tramites, total_opas, total_faqs, total_contenido');

    type StatsAccumulator = {
      total_dependencias: number;
      total_subdependencias: number;
      total_tramites: number;
      total_opas: number;
      total_faqs: number;
      total_contenido: number;
    };

    const totalStats = stats?.reduce((acc: StatsAccumulator, dep) => ({
      total_dependencias: acc.total_dependencias + 1,
      total_subdependencias: acc.total_subdependencias + dep.total_subdependencias,
      total_tramites: acc.total_tramites + dep.total_tramites,
      total_opas: acc.total_opas + dep.total_opas,
      total_faqs: acc.total_faqs + dep.total_faqs,
      total_contenido: acc.total_contenido + dep.total_contenido
    }), {
      total_dependencias: 0,
      total_subdependencias: 0,
      total_tramites: 0,
      total_opas: 0,
      total_faqs: 0,
      total_contenido: 0
    } as StatsAccumulator);

    return NextResponse.json({
      success: true,
      data: dependencias,
      pagination: {
        limit,
        offset,
        total: count || 0
      },
      stats: totalStats
    });

  } catch (error) {
    console.error('Unexpected error in dependencias API:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/dependencias
 * Acciones adicionales para dependencias
 */
export async function POST(request: NextRequest) {
  try {
    const { action } = await request.json();
    
    if (action === 'get_stats') {
      const supabase = await createServerSupabase();

      // Obtener estadísticas detalladas
      const { data: dependencias, error } = await supabase
        .from('dependencias_view')
        .select('*')
        .order('total_contenido', { ascending: false });

      if (error) {
        throw error;
      }

      const stats = {
        total_dependencias: dependencias?.length || 0,
        total_subdependencias: dependencias?.reduce((sum, d) => sum + d.total_subdependencias, 0) || 0,
        total_tramites: dependencias?.reduce((sum, d) => sum + d.total_tramites, 0) || 0,
        total_opas: dependencias?.reduce((sum, d) => sum + d.total_opas, 0) || 0,
        total_faqs: dependencias?.reduce((sum, d) => sum + d.total_faqs, 0) || 0,
        total_contenido: dependencias?.reduce((sum, d) => sum + d.total_contenido, 0) || 0,
        top_dependencias: dependencias?.slice(0, 5) || [],
        dependencias_sin_contenido: dependencias?.filter(d => d.total_contenido === 0).length || 0
      };

      return NextResponse.json({
        success: true,
        data: stats
      });
    }

    return NextResponse.json(
      { error: 'Acción no válida' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Error in dependencias POST:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
