import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase';

/**
 * GET /api/dependencias/[id]
 * Obtiene detalle de una dependencia específica con sus subdependencias
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const { searchParams } = new URL(request.url);
    const includeContent = searchParams.get('includeContent') === 'true';

    const supabase = await createServerSupabase();

    // Obtener información de la dependencia
    const { data: dependencia, error: depError } = await supabase
      .from('dependencias_view')
      .select('*')
      .eq('id', id)
      .single();

    if (depError || !dependencia) {
      return NextResponse.json(
        { error: 'Dependencia no encontrada' },
        { status: 404 }
      );
    }

    // Obtener subdependencias con métricas
    const { data: subdependencias, error: subError } = await supabase
      .from('subdependencias_detail_view')
      .select('*')
      .eq('dependencia_id', id)
      .order('nombre');

    if (subError) {
      console.error('Error fetching subdependencias:', subError);
      return NextResponse.json(
        { error: 'Error al obtener subdependencias' },
        { status: 500 }
      );
    }

    let contenido = null;

    // Si se solicita, incluir el contenido completo
    if (includeContent) {
      const subdependenciaIds = subdependencias?.map(s => s.id) || [];

      // Obtener trámites
      const { data: tramites } = await supabase
        .from('tramites_view')
        .select('*')
        .in('subdependencia_id', subdependenciaIds)
        .order('nombre');

      // Obtener OPAs
      const { data: opas } = await supabase
        .from('opas_view')
        .select('*')
        .in('subdependencia_id', subdependenciaIds)
        .order('nombre');

      // Obtener FAQs
      const { data: faqs } = await supabase
        .from('faqs_view')
        .select('*')
        .in('subdependencia_id', subdependenciaIds)
        .order('tema', { ascending: true });

      contenido = {
        tramites: tramites || [],
        opas: opas || [],
        faqs: faqs || []
      };
    }

    return NextResponse.json({
      success: true,
      data: {
        dependencia,
        subdependencias: subdependencias || [],
        contenido
      }
    });

  } catch (error) {
    console.error('Unexpected error in dependencia detail API:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/dependencias/[id]
 * Actualiza información de una dependencia (solo para usuarios autorizados)
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const updates = await request.json();

    const supabase = await createServerSupabase();

    // Verificar autenticación
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'No autorizado' },
        { status: 401 }
      );
    }

    // Verificar permisos
    const { data: hasPermission } = await supabase
      .rpc('check_user_permission', {
        p_user_id: user.id,
        p_action: 'UPDATE',
        p_table_name: 'dependencias',
        p_dependencia_id: id
      });

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Sin permisos para modificar esta dependencia' },
        { status: 403 }
      );
    }

    // Actualizar dependencia
    const { data: updatedDependencia, error: updateError } = await supabase
      .schema('ingestion')
      .from('dependencias')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating dependencia:', updateError);
      return NextResponse.json(
        { error: 'Error al actualizar dependencia' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: updatedDependencia
    });

  } catch (error) {
    console.error('Error in dependencia PUT:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
