import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

/**
 * GET /api/dependencias/[id]
 * Obtiene detalle de una dependencia específica con sus subdependencias
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const { searchParams } = new URL(request.url);
    const includeContent = searchParams.get('includeContent') === 'true';

    // Crear cliente de Supabase con clave anónima
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        },
      }
    );

    // Obtener información de la dependencia
    const { data: dependencia, error: depError } = await supabase
      .schema('ingestion')
      .from('dependencias')
      .select('*')
      .eq('id', id)
      .eq('activo', true)
      .single();

    if (depError || !dependencia) {
      return NextResponse.json(
        { error: 'Dependencia no encontrada' },
        { status: 404 }
      );
    }

    // Obtener subdependencias
    const { data: subdependenciasRaw, error: subError } = await supabase
      .schema('ingestion')
      .from('subdependencias')
      .select('*')
      .eq('dependencia_id', id)
      .eq('activo', true)
      .order('nombre');

    if (subError) {
      console.error('Error fetching subdependencias:', subError);
      return NextResponse.json(
        { error: 'Error al obtener subdependencias' },
        { status: 500 }
      );
    }

    // Calcular métricas para cada subdependencia
    const subdependencias = await Promise.all(
      (subdependenciasRaw || []).map(async (subdep) => {
        // Contar trámites
        const { count: tramitesCount } = await supabase
          .schema('ingestion')
          .from('tramites')
          .select('*', { count: 'exact', head: true })
          .eq('subdependencia_id', subdep.id)
          .eq('activo', true);

        // Contar OPAs
        const { count: opasCount } = await supabase
          .schema('ingestion')
          .from('opas')
          .select('*', { count: 'exact', head: true })
          .eq('subdependencia_id', subdep.id)
          .eq('activo', true);

        // Contar FAQs
        const { count: faqsCount } = await supabase
          .schema('ingestion')
          .from('faqs')
          .select('*', { count: 'exact', head: true })
          .eq('subdependencia_id', subdep.id)
          .eq('activo', true);

        return {
          ...subdep,
          total_tramites: tramitesCount || 0,
          total_opas: opasCount || 0,
          total_faqs: faqsCount || 0,
          total_contenido: (tramitesCount || 0) + (opasCount || 0) + (faqsCount || 0)
        };
      })
    );

    // Calcular métricas para la dependencia
    const total_subdependencias = subdependencias.length;
    const total_tramites = subdependencias.reduce((sum, s) => sum + s.total_tramites, 0);
    const total_opas = subdependencias.reduce((sum, s) => sum + s.total_opas, 0);
    const total_faqs = subdependencias.reduce((sum, s) => sum + s.total_faqs, 0);
    const total_contenido = total_tramites + total_opas + total_faqs;

    const dependenciaWithMetrics = {
      ...dependencia,
      total_subdependencias,
      total_tramites,
      total_opas,
      total_faqs,
      total_contenido,
      subdependencias
    };

    let contenido = null;

    // Si se solicita, incluir el contenido completo
    if (includeContent) {
      const subdependenciaIds = subdependencias?.map(s => s.id) || [];

      // Obtener trámites con información de subdependencia
      const { data: tramites } = await supabase
        .schema('ingestion')
        .from('tramites')
        .select(`
          *,
          subdependencias!inner(nombre)
        `)
        .eq('dependencia_id', id)
        .eq('activo', true)
        .order('nombre');

      // Obtener OPAs con información de subdependencia
      const { data: opas } = await supabase
        .schema('ingestion')
        .from('opas')
        .select(`
          *,
          subdependencias!inner(nombre)
        `)
        .eq('dependencia_id', id)
        .eq('activo', true)
        .order('descripcion');

      // Obtener FAQs con información de subdependencia
      const { data: faqs } = await supabase
        .schema('ingestion')
        .from('faqs')
        .select(`
          *,
          subdependencias!inner(nombre)
        `)
        .eq('dependencia_id', id)
        .eq('activo', true)
        .order('pregunta');

      contenido = {
        tramites: (tramites || []).map(t => ({
          ...t,
          subdependencia_nombre: (t.subdependencias as any)?.nombre || 'N/A'
        })),
        opas: (opas || []).map(o => ({
          ...o,
          subdependencia_nombre: (o.subdependencias as any)?.nombre || 'N/A'
        })),
        faqs: (faqs || []).map(f => ({
          ...f,
          subdependencia_nombre: (f.subdependencias as any)?.nombre || 'N/A'
        }))
      };
    }

    return NextResponse.json({
      success: true,
      data: {
        ...dependenciaWithMetrics,
        contenido
      }
    });

  } catch (error) {
    console.error('Unexpected error in dependencia detail API:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/dependencias/[id]
 * Actualiza información de una dependencia (solo para usuarios autorizados)
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const updates = await request.json();

    // Crear cliente admin de Supabase para operaciones de escritura
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        },
      }
    );

    // Verificar autenticación
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'No autorizado' },
        { status: 401 }
      );
    }

    // Verificar permisos
    const { data: hasPermission } = await supabase
      .rpc('check_user_permission', {
        p_user_id: user.id,
        p_action: 'UPDATE',
        p_table_name: 'dependencias',
        p_dependencia_id: id
      });

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Sin permisos para modificar esta dependencia' },
        { status: 403 }
      );
    }

    // Actualizar dependencia
    const { data: updatedDependencia, error: updateError } = await supabase
      .schema('ingestion')
      .from('dependencias')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating dependencia:', updateError);
      return NextResponse.json(
        { error: 'Error al actualizar dependencia' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: updatedDependencia
    });

  } catch (error) {
    console.error('Error in dependencia PUT:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
