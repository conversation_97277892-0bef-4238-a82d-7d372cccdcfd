import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase-server';

/**
 * GET /api/tramites/[id]
 * Obtiene detalle de un trámite específico
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const supabase = await createServerSupabase();

    const { data: tramite, error } = await supabase
      .from('tramites_view')
      .select('*')
      .eq('id', id)
      .single();

    if (error || !tramite) {
      return NextResponse.json(
        { error: 'Trámite no encontrado' },
        { status: 404 }
      );
    }

    // Transform data to match frontend expectations
    const transformedTramite = {
      id: tramite.id,
      nombre: tramite.nombre,
      descripcion: tramite.descripcion || '',
      categoria: tramite.categoria || 'General',
      tiempoRespuesta: tramite.tiempo_estimado || 'No especificado',
      tienePago: tramite.costo ? 'Sí' : 'No',
      costoDetalle: tramite.costo,
      modalidad: tramite.modalidad || 'Presencial',
      requisitos: tramite.requisitos || [],
      documentosRequeridos: tramite.documentos_requeridos || [],
      urlSuit: tramite.url_suit || '',
      urlGovco: tramite.url_govco || '',
      popularidad: 0,
      satisfaccion: 0,
      dependencia: {
        id: tramite.dependencia_id,
        codigo: tramite.dependencia_codigo,
        nombre: tramite.dependencia_nombre,
        sigla: tramite.dependencia_sigla
      },
      subdependencia: {
        id: tramite.subdependencia_id,
        nombre: tramite.subdependencia_nombre
      }
    };

    return NextResponse.json({
      success: true,
      data: transformedTramite
    });

  } catch (error) {
    console.error('Unexpected error in tramite detail API:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/tramites/[id]
 * Actualiza un trámite existente
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const updates = await request.json();

    const supabase = await createServerSupabase();

    // Verificar autenticación
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'No autorizado' },
        { status: 401 }
      );
    }

    // Obtener trámite actual para verificar permisos
    const { data: currentTramite, error: fetchError } = await supabase
      .schema('ingestion')
      .from('tramites')
      .select('subdependencia_id, subdependencias(dependencia_id)')
      .eq('id', id)
      .single();

    if (fetchError || !currentTramite) {
      return NextResponse.json(
        { error: 'Trámite no encontrado' },
        { status: 404 }
      );
    }

    // Verificar permisos
    const { data: hasPermission } = await supabase
      .rpc('check_user_permission', {
        p_user_id: user.id,
        p_action: 'UPDATE',
        p_table_name: 'tramites',
        p_dependencia_id: currentTramite.subdependencias?.dependencia_id
      });

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Sin permisos para modificar este trámite' },
        { status: 403 }
      );
    }

    // Actualizar trámite
    const { data: updatedTramite, error: updateError } = await supabase
      .schema('ingestion')
      .from('tramites')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating tramite:', updateError);
      return NextResponse.json(
        { error: 'Error al actualizar trámite' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: updatedTramite
    });

  } catch (error) {
    console.error('Error in tramite PUT:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/tramites/[id]
 * Desactiva un trámite (soft delete)
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const supabase = await createServerSupabase();

    // Verificar autenticación
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'No autorizado' },
        { status: 401 }
      );
    }

    // Obtener trámite actual para verificar permisos
    const { data: currentTramite, error: fetchError } = await supabase
      .schema('ingestion')
      .from('tramites')
      .select('subdependencia_id, subdependencias(dependencia_id)')
      .eq('id', id)
      .single();

    if (fetchError || !currentTramite) {
      return NextResponse.json(
        { error: 'Trámite no encontrado' },
        { status: 404 }
      );
    }

    // Verificar permisos
    const { data: hasPermission } = await supabase
      .rpc('check_user_permission', {
        p_user_id: user.id,
        p_action: 'DELETE',
        p_table_name: 'tramites',
        p_dependencia_id: currentTramite.subdependencias?.dependencia_id
      });

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Sin permisos para eliminar este trámite' },
        { status: 403 }
      );
    }

    // Desactivar trámite (soft delete)
    const { error: deleteError } = await supabase
      .schema('ingestion')
      .from('tramites')
      .update({
        activo: false,
        updated_at: new Date().toISOString()
      })
      .eq('id', id);

    if (deleteError) {
      console.error('Error deactivating tramite:', deleteError);
      return NextResponse.json(
        { error: 'Error al eliminar trámite' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Trámite eliminado correctamente'
    });

  } catch (error) {
    console.error('Error in tramite DELETE:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
