'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import MainNavigation from '@/components/layout/MainNavigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  ArrowLeft, 
  Building2, 
  FileText, 
  HelpCircle, 
  Users, 
  ChevronRight,
  ExternalLink 
} from 'lucide-react';
import Link from 'next/link';

interface Subdependencia {
  id: string;
  nombre: string;
  descripcion: string;
  total_tramites: number;
  total_opas: number;
  total_faqs: number;
  total_contenido: number;
}

interface DependenciaDetail {
  id: string;
  nombre: string;
  descripcion: string;
  total_subdependencias: number;
  total_tramites: number;
  total_opas: number;
  total_faqs: number;
  total_contenido: number;
  subdependencias: Subdependencia[];
}

export default function DependenciaDetailPage() {
  const params = useParams();
  const dependenciaId = params.id as string;
  
  const [dependencia, setDependencia] = useState<DependenciaDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (dependenciaId) {
      fetchDependenciaDetail();
    }
  }, [dependenciaId]);

  const fetchDependenciaDetail = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/dependencias/${dependenciaId}?include_subdependencias=true`);
      
      if (!response.ok) {
        throw new Error('Error al cargar detalle de dependencia');
      }

      const data = await response.json();
      
      if (data.success) {
        setDependencia(data.data);
      } else {
        throw new Error(data.error || 'Error desconocido');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error desconocido');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <MainNavigation />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Cargando información...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !dependencia) {
    return (
      <div className="min-h-screen bg-gray-50">
        <MainNavigation />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <div className="text-red-500 text-xl mb-4">⚠️</div>
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Error al cargar</h2>
              <p className="text-gray-600 mb-4">{error || 'Dependencia no encontrada'}</p>
              <Link href="/dependencias">
                <Button>
                  Volver a Dependencias
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <MainNavigation />
      
      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 text-sm text-gray-600 mb-6">
          <Link href="/dependencias" className="hover:text-blue-600">
            Dependencias
          </Link>
          <ChevronRight className="h-4 w-4" />
          <span className="text-gray-900">{dependencia.nombre}</span>
        </nav>

        {/* Back Button */}
        <div className="mb-6">
          <Link href="/dependencias">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Volver a Dependencias
            </Button>
          </Link>
        </div>

        {/* Header */}
        <div className="mb-8">
          <div className="flex items-start space-x-4">
            <div className="p-3 bg-blue-100 rounded-lg">
              <Building2 className="h-8 w-8 text-blue-600" />
            </div>
            <div className="flex-1">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                {dependencia.nombre}
              </h1>
              {dependencia.descripcion && (
                <p className="text-gray-600 text-lg">
                  {dependencia.descripcion}
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Building2 className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="text-sm text-gray-600">Subdependencias</p>
                  <p className="text-2xl font-bold text-gray-900">{dependencia.total_subdependencias}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <FileText className="h-5 w-5 text-green-600" />
                <div>
                  <p className="text-sm text-gray-600">Trámites</p>
                  <p className="text-2xl font-bold text-gray-900">{dependencia.total_tramites}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Users className="h-5 w-5 text-purple-600" />
                <div>
                  <p className="text-sm text-gray-600">OPAs</p>
                  <p className="text-2xl font-bold text-gray-900">{dependencia.total_opas}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <HelpCircle className="h-5 w-5 text-orange-600" />
                <div>
                  <p className="text-sm text-gray-600">FAQs</p>
                  <p className="text-2xl font-bold text-gray-900">{dependencia.total_faqs}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Content Tabs */}
        <Tabs defaultValue="subdependencias" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="subdependencias">Subdependencias</TabsTrigger>
            <TabsTrigger value="tramites">Trámites</TabsTrigger>
            <TabsTrigger value="opas">OPAs</TabsTrigger>
            <TabsTrigger value="faqs">FAQs</TabsTrigger>
          </TabsList>

          <TabsContent value="subdependencias" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {dependencia.subdependencias?.map((subdep) => (
                <Card key={subdep.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <CardTitle className="text-lg">{subdep.nombre}</CardTitle>
                    {subdep.descripcion && (
                      <CardDescription className="text-sm">
                        {subdep.descripcion}
                      </CardDescription>
                    )}
                  </CardHeader>
                  
                  <CardContent>
                    <div className="space-y-3">
                      <div className="grid grid-cols-3 gap-2 text-xs">
                        <div className="text-center">
                          <div className="font-semibold text-green-600">{subdep.total_tramites}</div>
                          <div className="text-gray-500">Trámites</div>
                        </div>
                        <div className="text-center">
                          <div className="font-semibold text-purple-600">{subdep.total_opas}</div>
                          <div className="text-gray-500">OPAs</div>
                        </div>
                        <div className="text-center">
                          <div className="font-semibold text-orange-600">{subdep.total_faqs}</div>
                          <div className="text-gray-500">FAQs</div>
                        </div>
                      </div>
                      
                      <div className="pt-2 border-t">
                        <Badge variant="outline" className="w-full justify-center">
                          {subdep.total_contenido} contenidos totales
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="tramites" className="space-y-4">
            <Card>
              <CardContent className="p-6 text-center">
                <FileText className="h-12 w-12 text-green-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Trámites de {dependencia.nombre}</h3>
                <p className="text-gray-600 mb-4">
                  Explora los {dependencia.total_tramites} trámites disponibles en esta dependencia
                </p>
                <Link href={`/tramites?dependencia=${dependencia.id}`}>
                  <Button>
                    Ver Trámites
                    <ExternalLink className="h-4 w-4 ml-2" />
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="opas" className="space-y-4">
            <Card>
              <CardContent className="p-6 text-center">
                <Users className="h-12 w-12 text-purple-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">OPAs de {dependencia.nombre}</h3>
                <p className="text-gray-600 mb-4">
                  Consulta los {dependencia.total_opas} procedimientos administrativos de esta dependencia
                </p>
                <Link href={`/opas?dependencia=${dependencia.id}`}>
                  <Button>
                    Ver OPAs
                    <ExternalLink className="h-4 w-4 ml-2" />
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="faqs" className="space-y-4">
            <Card>
              <CardContent className="p-6 text-center">
                <HelpCircle className="h-12 w-12 text-orange-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">FAQs de {dependencia.nombre}</h3>
                <p className="text-gray-600 mb-4">
                  Encuentra respuestas en las {dependencia.total_faqs} preguntas frecuentes de esta dependencia
                </p>
                <Link href={`/faqs?dependencia=${dependencia.id}`}>
                  <Button>
                    Ver FAQs
                    <ExternalLink className="h-4 w-4 ml-2" />
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
