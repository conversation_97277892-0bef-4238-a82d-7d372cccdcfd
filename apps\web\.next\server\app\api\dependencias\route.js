/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/dependencias/route";
exports.ids = ["app/api/dependencias/route"];
exports.modules = {

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdependencias%2Froute&page=%2Fapi%2Fdependencias%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdependencias%2Froute.ts&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdependencias%2Froute&page=%2Fapi%2Fdependencias%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdependencias%2Froute.ts&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/../../node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/../../node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/../../node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Juan_Pulgarin_Documents_augment_projects_chia_next_apps_web_app_api_dependencias_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/dependencias/route.ts */ \"(rsc)/./app/api/dependencias/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/dependencias/route\",\n        pathname: \"/api/dependencias\",\n        filename: \"route\",\n        bundlePath: \"app/api/dependencias/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\api\\\\dependencias\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Juan_Pulgarin_Documents_augment_projects_chia_next_apps_web_app_api_dependencias_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdependencias%2Froute&page=%2Fapi%2Fdependencias%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdependencias%2Froute.ts&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!**********************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \**********************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./app/api/dependencias/route.ts":
/*!***************************************!*\
  !*** ./app/api/dependencias/route.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/../../node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase */ \"(rsc)/./lib/supabase.ts\");\n\n\n/**\n * GET /api/dependencias\n * Obtiene lista de dependencias con métricas de contenido\n */ async function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const search = searchParams.get('search');\n        const hasContent = searchParams.get('hasContent'); // 'tramites', 'opas', 'faqs', 'any'\n        const sortBy = searchParams.get('sortBy') || 'nombre'; // 'nombre', 'total_contenido'\n        const order = searchParams.get('order') || 'asc'; // 'asc', 'desc'\n        const limit = parseInt(searchParams.get('limit') || '50');\n        const offset = parseInt(searchParams.get('offset') || '0');\n        const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.createServerSupabase)();\n        let query = supabase.from('dependencias_view').select('*');\n        // Aplicar filtro de búsqueda\n        if (search) {\n            query = query.ilike('nombre', `%${search}%`);\n        }\n        // Aplicar filtro por tipo de contenido\n        if (hasContent) {\n            switch(hasContent){\n                case 'tramites':\n                    query = query.gt('total_tramites', 0);\n                    break;\n                case 'opas':\n                    query = query.gt('total_opas', 0);\n                    break;\n                case 'faqs':\n                    query = query.gt('total_faqs', 0);\n                    break;\n                case 'any':\n                    query = query.gt('total_contenido', 0);\n                    break;\n            }\n        }\n        // Aplicar ordenamiento\n        const ascending = order === 'asc';\n        query = query.order(sortBy, {\n            ascending\n        });\n        // Aplicar paginación\n        query = query.range(offset, offset + limit - 1);\n        const { data: dependencias, error, count } = await query;\n        if (error) {\n            console.error('Error fetching dependencias:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Error al obtener dependencias'\n            }, {\n                status: 500\n            });\n        }\n        // Obtener estadísticas generales\n        const { data: stats } = await supabase.from('dependencias_view').select('total_subdependencias, total_tramites, total_opas, total_faqs, total_contenido');\n        const totalStats = stats?.reduce((acc, dep)=>({\n                total_dependencias: (acc.total_dependencias || 0) + 1,\n                total_subdependencias: acc.total_subdependencias + dep.total_subdependencias,\n                total_tramites: acc.total_tramites + dep.total_tramites,\n                total_opas: acc.total_opas + dep.total_opas,\n                total_faqs: acc.total_faqs + dep.total_faqs,\n                total_contenido: acc.total_contenido + dep.total_contenido\n            }), {\n            total_dependencias: 0,\n            total_subdependencias: 0,\n            total_tramites: 0,\n            total_opas: 0,\n            total_faqs: 0,\n            total_contenido: 0\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: dependencias,\n            pagination: {\n                limit,\n                offset,\n                total: count || 0\n            },\n            stats: totalStats\n        });\n    } catch (error) {\n        console.error('Unexpected error in dependencias API:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Error interno del servidor'\n        }, {\n            status: 500\n        });\n    }\n}\n/**\n * POST /api/dependencias\n * Acciones adicionales para dependencias\n */ async function POST(request) {\n    try {\n        const { action } = await request.json();\n        if (action === 'get_stats') {\n            const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.createServerSupabase)();\n            // Obtener estadísticas detalladas\n            const { data: dependencias, error } = await supabase.from('dependencias_view').select('*').order('total_contenido', {\n                ascending: false\n            });\n            if (error) {\n                throw error;\n            }\n            const stats = {\n                total_dependencias: dependencias?.length || 0,\n                total_subdependencias: dependencias?.reduce((sum, d)=>sum + d.total_subdependencias, 0) || 0,\n                total_tramites: dependencias?.reduce((sum, d)=>sum + d.total_tramites, 0) || 0,\n                total_opas: dependencias?.reduce((sum, d)=>sum + d.total_opas, 0) || 0,\n                total_faqs: dependencias?.reduce((sum, d)=>sum + d.total_faqs, 0) || 0,\n                total_contenido: dependencias?.reduce((sum, d)=>sum + d.total_contenido, 0) || 0,\n                top_dependencias: dependencias?.slice(0, 5) || [],\n                dependencias_sin_contenido: dependencias?.filter((d)=>d.total_contenido === 0).length || 0\n            };\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: stats\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Acción no válida'\n        }, {\n            status: 400\n        });\n    } catch (error) {\n        console.error('Error in dependencias POST:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Error interno del servidor'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/dependencias/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/supabase.ts":
/*!*************************!*\
  !*** ./lib/supabase.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAdminSupabase: () => (/* binding */ createAdminSupabase),\n/* harmony export */   createClientSupabase: () => (/* binding */ createClientSupabase),\n/* harmony export */   createServerSupabase: () => (/* binding */ createServerSupabase),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/../../node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(rsc)/../../node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__);\n\n\n// Environment variables validation\nconst supabaseUrl = \"https://hndowofzjzjoljnapokv.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhuZG93b2Z6anpqb2xqbmFwb2t2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE3Njc3MDgsImV4cCI6MjA2NzM0MzcwOH0.Q8zEYUAGPF2N5g_CsZYM0sMAPUF9caaBxhhbbvsp7yY\";\nif (!supabaseUrl || !supabaseAnonKey) {\n    throw new Error('Missing Supabase environment variables');\n}\n// Client-side Supabase client\nconst createClientSupabase = ()=>{\n    return (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__.createClientComponentClient)();\n};\n// Server-side Supabase client - dynamic import to avoid build issues\nconst createServerSupabase = async ()=>{\n    const { cookies } = await __webpack_require__.e(/*! import() */ \"vendor-chunks/next\").then(__webpack_require__.bind(__webpack_require__, /*! next/headers */ \"(rsc)/../../node_modules/next/dist/api/headers.js\"));\n    const cookieStore = cookies();\n    return (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__.createServerComponentClient)({\n        cookies: ()=>cookieStore\n    });\n};\n// Admin client for server-side operations\nconst createAdminSupabase = ()=>{\n    const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n    if (!serviceRoleKey) {\n        throw new Error('Missing Supabase service role key');\n    }\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, serviceRoleKey, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n};\n// Default client export for backward compatibility\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseAnonKey);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/supabase.ts\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!**********************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \**********************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?3713":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?8e41":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/jose","vendor-chunks/isows","vendor-chunks/tr46","vendor-chunks/webidl-conversions","vendor-chunks/set-cookie-parser"], () => (__webpack_exec__("(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdependencias%2Froute&page=%2Fapi%2Fdependencias%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdependencias%2Froute.ts&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();